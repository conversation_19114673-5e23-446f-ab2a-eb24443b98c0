{% load static %} 

{% comment %} Authentication URLs {% endcomment %}
{% url 'account_login' as login_url %} 
{% url 'account_signup' as signup_url %} 
{% url 'account_logout' as logout_url %}


<!DOCTYPE html>
<html class="h-100" lang="en">
    <head>
        {% comment %} Meta Information {% endcomment %}
        <title>NederLearn</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">

        {% comment %} External Resources {% endcomment %}
        {% comment %} Google Fonts Setup {% endcomment %}
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet" >

        {% comment %} CSS Dependencies {% endcomment %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
        <link rel="stylesheet" href="{% static 'css/style.css' %}">

        {% comment %} JavaScript Dependencies {% endcomment %}
        <script src="https://code.jquery.com/jquery-3.6.0.min.js" crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
        <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="icon" type="image/x-icon" href="{% static 'images/nederlearn_flavicon.webp' %}">
    </head>


<body class="d-flex flex-column h-100">

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark"
        aria-label="Main navigation">
        <div class="container-fluid mx-3">
            <!-- Brand Logo and Link -->
            <a class="navbar-logo me-2"
                href="{% if user.is_authenticated %}{% url 'home' %}{% else %}{% url 'account_login' %}{% endif %}"
                aria-label="Home">NederLearn</a>

            <!-- Navbar Toggler for Mobile View -->
            <button class="navbar-toggler" type="button"
                data-bs-toggle="collapse" data-bs-target="#navbarContent"
                aria-controls="navbarContent" aria-expanded="false"
                aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navbar Items -->
            <div class="collapse navbar-collapse" id="navbarContent">
                <ul class="navbar-nav me-auto">
                    <!-- About Us Link -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'about_us' %}">
                            <i class="fas fa-info-circle"></i> About Us
                        </a>
                    </li>
                    <!-- Conditional Signup Link -->
                    {% if not user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'account_signup' %}">
                            <i class="fas fa-user-friends"></i> Join the Community
                        </a>
                    </li>
                    {% endif %}
                </ul>

                {% if user.is_authenticated %}
                <ul class="navbar-nav ms-auto">
                    <!-- Links for authenticated users -->
                    <!-- Latest Posts Link -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">
                            <i class="fas fa-home"></i> Latest Posts
                        </a>
                    </li>
                    <!-- Create New Post Link -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'blogpost_create' %}">
                            <i class="fas fa-plus"></i>
                            Create New Post
                        </a>
                    </li>
                    <!-- My Posts Link -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'my_posts' %}">
                            <i class="fas fa-file-alt"></i> My Posts
                        </a>
                    </li>
                    <!-- Bookmarked Link -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'bookmarked' %}">
                            <i class="fas fa-bookmark"></i> Bookmarked
                        </a>
                    </li>
                    <!-- Dropdown for User Profile -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#"
                            id="navbarDropdownMenuLink" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false"
                            aria-label="Dropdown User Menu">
                            <i class="fas fa-user-circle fa-lg fa-2x"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end"
                            aria-labelledby="navbarDropdownMenuLink">
                            <!-- My Profile Link -->
                            <li><a class="dropdown-item"
                                    href="{% url 'profile' %}"><i
                                        class="fas fa-user-circle"></i> My
                                    Profile</a></li>
                            <!-- Edit My Profile Link -->
                            <li><a class="dropdown-item"
                                    href="{% url 'profile_edit' %}"><i
                                        class="fas fa-edit"></i> Edit
                                    Profile</a></li>
                            <!-- Manage Account Link -->
                            <li><a class="dropdown-item"
                                    href="{% url 'account_delete' request.user.pk %}"><i
                                        class="fas fa-cog"></i> Manage
                                    Account</a></li>
                            <!-- Log Out Link -->
                            <li><a class="dropdown-item"
                                    href="{% url 'account_logout' %}"><i
                                        class="fas fa-sign-out-alt"></i> Log
                                    Out</a>
                            </li>
                        </ul>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Alert Messages Section -->
    <div class="alert-container">
        <div class="container">
            <div class="row">
                <div class="col-md-8 offset-md-2">
                    <!-- Loop through Django Messages -->
                    {% for message in messages %}
                    <div class="alert {{ message.tags }} alert-dismissible fade show message-alert"
                        role="alert" aria-label="Site Message">
                        <!-- Message Content -->
                        {{ message | safe }}
                        <!-- Dismiss Button for Alert -->
                        <button type="button" class="btn-close"
                            data-bs-dismiss="alert"
                            aria-label="Close Alert Message"></button>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>


    <!-- Main Content Section -->
    <main class="flex-shrink-0 container my-4">
        {% block content %}
        <!-- Placeholder for Page Specific Content -->
        {% endblock content %}
    </main>

      <!-- Footer Section -->
      <footer class="footer mt-auto py-3 dark-bg" aria-label="Footer">
        <div class="container text-center">
            <p class="m-0">© 2025 NederLearn</p>
        </div>
    </footer>
    <!-- JavaScript for Message Dismissal -->
    <script>
        setTimeout(function () {
            let messages = document.querySelectorAll(
                '.message-alert');
            messages.forEach(function (message) {
                let alert = new bootstrap.Alert(
                    message);
                alert.close();
        });
        }, 2000);
    </script>
    <!-- Scripts to extend to index.html -->
    {% block scripts %}
    {% endblock scripts %}
</body>

</html>